"""
Prometheus metrics for voice-gateway service.

This module defines custom metrics to monitor voice processing pipeline performance.
"""

from fastapi import FastAPI
from cortexacommon.logging import get_logger
from prometheus_client import Counter, Histogram, Gauge, Info
from prometheus_fastapi_instrumentator import Instrumentator

logger = get_logger(__name__)

# Connection metrics
active_connections = Gauge(
    'voice_gateway_active_connections',
    'Number of active WebSocket connections'
)

total_connections = Counter(
    'voice_gateway_total_connections',
    'Total number of WebSocket connections created'
)

# Audio processing metrics
audio_bytes_received = Counter(
    'voice_gateway_audio_bytes_received_total',
    'Total bytes of audio received',
    ['call_id']
)

audio_bytes_sent = Counter(
    'voice_gateway_audio_bytes_sent_total',
    'Total bytes of audio sent',
    ['call_id']
)

# Speech processing pipeline metrics
speech_segments_processed = Counter(
    'voice_gateway_speech_segments_processed_total',
    'Total number of speech segments processed',
    ['call_id', 'provider']
)

speech_processing_duration = Histogram(
    'voice_gateway_speech_processing_duration_seconds',
    'Time spent processing speech segments',
    ['call_id', 'stage'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

# STT metrics
stt_requests = Counter(
    'voice_gateway_stt_requests_total',
    'Total STT requests',
    ['provider', 'status']
)

stt_duration = Histogram(
    'voice_gateway_stt_duration_seconds',
    'STT processing duration',
    ['provider'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

stt_confidence = Histogram(
    'voice_gateway_stt_confidence',
    'STT confidence scores',
    ['provider'],
    buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

# Translation metrics
translation_requests = Counter(
    'voice_gateway_translation_requests_total',
    'Total translation requests',
    ['provider', 'status']
)

translation_duration = Histogram(
    'voice_gateway_translation_duration_seconds',
    'Translation processing duration',
    ['provider'],
    buckets=[0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5]
)

# TTS metrics
tts_requests = Counter(
    'voice_gateway_tts_requests_total',
    'Total TTS requests',
    ['provider', 'status']
)

tts_duration = Histogram(
    'voice_gateway_tts_duration_seconds',
    'TTS processing duration',
    ['provider'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

# Error metrics
processing_errors = Counter(
    'voice_gateway_processing_errors_total',
    'Total processing errors',
    ['call_id', 'error_type', 'component']
)

# Service info
service_info = Info(
    'voice_gateway_service_info',
    'Voice Gateway service information'
)

# Provider status
provider_status = Gauge(
    'voice_gateway_provider_status',
    'Provider initialization status (1=initialized, 0=not initialized)',
    ['provider_type', 'provider_name']
)


def setup_metrics(app: FastAPI) -> None:
    """
    Set up Prometheus metrics for the FastAPI application.

    Args:
        app: FastAPI application instance
    """

    # Initialize FastAPI instrumentator
    instrumentator = Instrumentator(
        excluded_handlers=["/metrics"],
        should_group_status_codes=True,
        should_ignore_untemplated=True,
        should_instrument_requests_inprogress=True,
        inprogress_name="http_requests_inprogress",
        inprogress_labels=True,
    )
    instrumentator.instrument(app, metric_subsystem="voice_gateway")

    logger.info("Exposing metrics endpoint at /metrics...")
    instrumentator.expose(app, endpoint="/metrics")

    logger.info("Prometheus metrics initialized successfully")


class MetricsCollector:
    """Helper class for collecting custom metrics."""
    
    @staticmethod
    def record_connection_opened(call_id: str):
        """Record a new WebSocket connection."""
        total_connections.inc()
        active_connections.inc()
    
    @staticmethod
    def record_connection_closed(call_id: str):
        """Record a closed WebSocket connection."""
        active_connections.dec()
    
    @staticmethod
    def record_audio_received(call_id: str, bytes_count: int):
        """Record audio bytes received."""
        audio_bytes_received.labels(call_id=call_id).inc(bytes_count)
    
    @staticmethod
    def record_audio_sent(call_id: str, bytes_count: int):
        """Record audio bytes sent."""
        audio_bytes_sent.labels(call_id=call_id).inc(bytes_count)
    
    @staticmethod
    def record_speech_segment_processed(call_id: str, provider: str):
        """Record a processed speech segment."""
        speech_segments_processed.labels(call_id=call_id, provider=provider).inc()
    
    @staticmethod
    def record_processing_duration(call_id: str, stage: str, duration: float):
        """Record processing duration for a stage."""
        speech_processing_duration.labels(call_id=call_id, stage=stage).observe(duration)
    
    @staticmethod
    def record_stt_request(provider: str, status: str, duration: float, confidence: float = None):
        """Record STT request metrics."""
        stt_requests.labels(provider=provider, status=status).inc()
        stt_duration.labels(provider=provider).observe(duration)
        if confidence is not None:
            stt_confidence.labels(provider=provider).observe(confidence)
    
    @staticmethod
    def record_translation_request(provider: str, status: str, duration: float):
        """Record translation request metrics."""
        translation_requests.labels(provider=provider, status=status).inc()
        translation_duration.labels(provider=provider).observe(duration)
    
    @staticmethod
    def record_tts_request(provider: str, status: str, duration: float):
        """Record TTS request metrics."""
        tts_requests.labels(provider=provider, status=status).inc()
        tts_duration.labels(provider=provider).observe(duration)
    
    @staticmethod
    def record_processing_error(call_id: str, error_type: str, component: str):
        """Record a processing error."""
        processing_errors.labels(
            call_id=call_id, 
            error_type=error_type, 
            component=component
        ).inc()
    
    @staticmethod
    def set_provider_status(provider_type: str, provider_name: str, initialized: bool):
        """Set provider initialization status."""
        provider_status.labels(
            provider_type=provider_type,
            provider_name=provider_name
        ).set(1 if initialized else 0)
