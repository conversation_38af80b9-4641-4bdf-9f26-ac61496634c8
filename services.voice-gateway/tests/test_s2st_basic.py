import pytest
from unittest.mock import MagicMock, patch


class TestS2STBasicFunctionality:
    """Test basic S2ST functionality without heavy dependencies."""

    def test_vad_processor_interface(self):
        """Test VAD processor interface without webrtcvad."""
        with patch('webrtcvad.Vad') as mock_vad_class:
            mock_vad_instance = MagicMock()
            mock_vad_instance.is_speech.return_value = True
            mock_vad_class.return_value = mock_vad_instance
            
            # Import after mocking
            from src.pipeline.s2st import VADProcessor
            
            vad = VADProcessor(aggressiveness=2, frame_duration_ms=30)
            
            # Test basic properties
            assert vad.aggressiveness == 2
            assert vad.frame_duration_ms == 30
            assert vad.frame_size == 480  # 30ms at 16kHz

    def test_vad_speech_detection_mock(self):
        """Test VAD speech detection with mocked webrtcvad."""
        with patch('webrtcvad.Vad') as mock_vad_class:
            mock_vad_instance = MagicMock()
            mock_vad_instance.is_speech.return_value = True
            mock_vad_class.return_value = mock_vad_instance
            
            from src.pipeline.s2st import VADProcessor
            
            vad = VADProcessor()
            audio_frame = b'\x00' * 960  # Proper frame size
            
            result = vad.is_speech(audio_frame)
            assert result is True
            mock_vad_instance.is_speech.assert_called_once()

    def test_vad_no_webrtcvad_fallback(self):
        """Test VAD fallback when webrtcvad is not available."""
        with patch('webrtcvad', None):
            from src.pipeline.s2st import VADProcessor
            
            vad = VADProcessor()
            audio_frame = b'\x00' * 960
            
            # Should return True when VAD is not available
            result = vad.is_speech(audio_frame)
            assert result is True

    @pytest.mark.asyncio
    async def test_stt_processor_interface(self):
        """Test STT processor interface without faster_whisper."""
        with patch('faster_whisper.WhisperModel') as mock_whisper_class:
            mock_whisper_instance = MagicMock()
            mock_whisper_instance.transcribe.return_value = (
                [{"text": "Hello world", "start": 0.0, "end": 2.0}],
                {"language": "en", "language_probability": 0.95}
            )
            mock_whisper_class.return_value = mock_whisper_instance
            
            from src.pipeline.s2st import STTProcessor
            
            stt = STTProcessor()
            audio_data = b"fake_audio_data"
            
            text, confidence = await stt.transcribe(audio_data)
            
            assert text == "Hello world"
            assert confidence == 0.95
            mock_whisper_instance.transcribe.assert_called_once()

    @pytest.mark.asyncio
    async def test_translation_processor_interface(self):
        """Test translation processor interface without transformers."""
        with patch('transformers.pipeline') as mock_pipeline_func:
            mock_translator = MagicMock()
            mock_translator.return_value = [{"translation_text": "Hola mundo"}]
            mock_pipeline_func.return_value = mock_translator
            
            from src.pipeline.s2st import TranslationProcessor
            
            translator = TranslationProcessor()
            text = "Hello world"
            
            result = await translator.translate(text)
            
            assert result == "Hola mundo"
            mock_translator.assert_called_once_with(text)

    @pytest.mark.asyncio
    async def test_tts_processor_interface(self):
        """Test TTS processor interface without OpenAI."""
        with patch('openai.OpenAI') as mock_openai_class:
            mock_client = MagicMock()
            mock_response = MagicMock()
            mock_response.content = b"fake_tts_audio"
            mock_client.audio.speech.create.return_value = mock_response
            mock_openai_class.return_value = mock_client
            
            from src.pipeline.s2st import TTSProcessor
            
            tts = TTSProcessor()
            text = "Hello world"
            
            result = await tts.synthesize(text)
            
            assert result == b"fake_tts_audio"
            mock_client.audio.speech.create.assert_called_once()

    def test_s2st_processor_initialization(self):
        """Test S2ST processor initialization with mocked components."""
        with patch('webrtcvad.Vad'), \
             patch('faster_whisper.WhisperModel'), \
             patch('transformers.pipeline'), \
             patch('openai.OpenAI'):
            
            from src.pipeline.s2st import S2STProcessor
            
            processor = S2STProcessor()
            
            # Test basic properties
            assert processor.min_speech_duration == 1.0
            assert processor.max_silence_duration == 2.0
            assert isinstance(processor.speech_buffer, bytearray)
            assert processor.last_speech_time == 0.0
            assert processor.is_in_speech is False

    @pytest.mark.asyncio
    async def test_error_handling_stt_failure(self):
        """Test STT error handling."""
        with patch('faster_whisper.WhisperModel') as mock_whisper_class:
            mock_whisper_instance = MagicMock()
            mock_whisper_instance.transcribe.side_effect = Exception("STT failed")
            mock_whisper_class.return_value = mock_whisper_instance
            
            from src.pipeline.s2st import STTProcessor
            
            stt = STTProcessor()
            audio_data = b"fake_audio_data"
            
            text, confidence = await stt.transcribe(audio_data)
            
            # Should return empty results on error
            assert text == ""
            assert confidence == 0.0

    @pytest.mark.asyncio
    async def test_error_handling_translation_failure(self):
        """Test translation error handling."""
        with patch('transformers.pipeline') as mock_pipeline_func:
            mock_translator = MagicMock()
            mock_translator.side_effect = Exception("Translation failed")
            mock_pipeline_func.return_value = mock_translator
            
            from src.pipeline.s2st import TranslationProcessor
            
            translator = TranslationProcessor()
            text = "Hello world"
            
            result = await translator.translate(text)
            
            # Should return original text on error
            assert result == text

    @pytest.mark.asyncio
    async def test_error_handling_tts_failure(self):
        """Test TTS error handling."""
        with patch('openai.OpenAI') as mock_openai_class:
            mock_client = MagicMock()
            mock_client.audio.speech.create.side_effect = Exception("TTS failed")
            mock_openai_class.return_value = mock_client
            
            from src.pipeline.s2st import TTSProcessor
            
            tts = TTSProcessor()
            text = "Hello world"
            
            result = await tts.synthesize(text)
            
            # Should return None on error
            assert result is None

    def test_empty_text_handling(self):
        """Test handling of empty text inputs."""
        with patch('transformers.pipeline') as mock_pipeline_func, \
             patch('openai.OpenAI') as mock_openai_class:
            
            mock_translator = MagicMock()
            mock_pipeline_func.return_value = mock_translator
            
            mock_client = MagicMock()
            mock_openai_class.return_value = mock_client
            
            from src.pipeline.s2st import TranslationProcessor, TTSProcessor
            
            # Test translation with empty text
            translator = TranslationProcessor()
            result = translator.translate("")
            assert result == ""
            mock_translator.assert_not_called()
            
            # Test TTS with empty text
            tts = TTSProcessor()
            result = tts.synthesize("")
            assert result is None
            mock_client.audio.speech.create.assert_not_called()

    def test_vad_frame_size_validation(self):
        """Test VAD frame size validation."""
        with patch('webrtcvad.Vad') as mock_vad_class:
            mock_vad_instance = MagicMock()
            mock_vad_class.return_value = mock_vad_instance
            
            from src.pipeline.s2st import VADProcessor
            
            vad = VADProcessor()
            
            # Test with wrong frame size
            wrong_size_frame = b'\x00' * 100
            result = vad.is_speech(wrong_size_frame)
            assert result is False
            
            # Should not call VAD with wrong size
            mock_vad_instance.is_speech.assert_not_called()

    @pytest.mark.asyncio
    async def test_multiple_transcript_segments(self):
        """Test STT with multiple transcript segments."""
        with patch('faster_whisper.WhisperModel') as mock_whisper_class:
            mock_whisper_instance = MagicMock()
            mock_whisper_instance.transcribe.return_value = (
                [
                    {"text": "Hello", "start": 0.0, "end": 1.0},
                    {"text": " world", "start": 1.0, "end": 2.0},
                    {"text": "!", "start": 2.0, "end": 2.5}
                ],
                {"language": "en", "language_probability": 0.90}
            )
            mock_whisper_class.return_value = mock_whisper_instance
            
            from src.pipeline.s2st import STTProcessor
            
            stt = STTProcessor()
            audio_data = b"fake_audio_data"
            
            text, confidence = await stt.transcribe(audio_data)
            
            assert text == "Hello world!"
            assert confidence == 0.90
