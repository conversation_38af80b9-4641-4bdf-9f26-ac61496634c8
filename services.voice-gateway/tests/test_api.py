import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch


class TestRootEndpoint:
    """Test suite for root endpoint."""

    def test_root_endpoint(self, test_client: TestClient):
        """Test root endpoint returns service information."""
        response = test_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["service"] == "Cortexa Voice Gateway"
        assert data["version"] == "0.1.0"
        assert data["description"] == "Real-time voice translation service"
        assert data["status"] == "running"


class TestHealthEndpoint:
    """Test suite for health check endpoint."""

    def test_health_endpoint(self, test_client: TestClient):
        """Test health endpoint returns service status."""
        response = test_client.get("/health")

        assert response.status_code == 200
        data = response.json()

        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
        assert "active_connections" in data
        assert "max_connections" in data
        assert "configuration" in data

    def test_health_endpoint_configuration(self, test_client: TestClient):
        """Test health endpoint includes configuration details."""
        response = test_client.get("/health")

        assert response.status_code == 200
        data = response.json()

        assert data["status"] == "healthy"
        config = data["configuration"]
        assert "whisper_model" in config
        assert "translation_model" in config
        assert "tts_provider" in config
        assert "vad_aggressiveness" in config


class TestMetricsEndpoint:
    """Test suite for metrics endpoint."""

    def test_metrics_endpoint(self, test_client: TestClient):
        """Test metrics endpoint returns service metrics."""
        response = test_client.get("/metrics")
        
        assert response.status_code == 200
        data = response.json()
        
        # Check basic metrics structure
        assert "active_connections" in data
        assert "total_audio_received_bytes" in data
        assert "total_audio_sent_bytes" in data
        assert "total_segments_processed" in data
        assert "total_errors" in data
        assert isinstance(data["active_connections"], int)

    @patch("src.pipeline.state.connection_state_manager.get_all_connections")
    def test_metrics_with_active_connections(self, mock_get_connections, test_client: TestClient, connection_state):
        """Test metrics endpoint with active connections."""
        # Mock active connections
        mock_get_connections.return_value = [connection_state]
        
        response = test_client.get("/metrics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["active_connections"] == 1
        assert data["total_audio_received_bytes"] >= 0
        assert data["total_audio_sent_bytes"] >= 0


class TestCallHealthEndpoint:
    """Test suite for call service health endpoint."""

    def test_call_health_endpoint(self, test_client: TestClient):
        """Test call health endpoint."""
        response = test_client.get("/api/v1/call/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "voice-gateway"
        assert "active_connections" in data
        assert "max_connections" in data

    @patch("src.pipeline.state.connection_state_manager.get_connection_count")
    def test_call_health_with_connections(self, mock_get_count, test_client: TestClient):
        """Test call health endpoint with connection count."""
        mock_get_count.return_value = 5

        response = test_client.get("/api/v1/call/health")

        assert response.status_code == 200
        data = response.json()

        assert data["active_connections"] == 5
        assert "max_connections" in data
        assert isinstance(data["max_connections"], int)


class TestCallStatsEndpoint:
    """Test suite for call statistics endpoint."""

    def test_call_stats_endpoint_empty(self, test_client: TestClient):
        """Test call stats endpoint with no active connections."""
        response = test_client.get("/api/v1/call/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["active_connections"] == 0
        assert "max_connections" in data
        assert data["connections"] == []

    @patch("src.pipeline.state.connection_state_manager.get_all_connections")
    def test_call_stats_with_connections(self, mock_get_connections, test_client: TestClient, connection_state):
        """Test call stats endpoint with active connections."""
        # Mock connection stats
        connection_state.get_stats = lambda: {
            "call_id": "test-call-123",
            "user_id": "test-user-123",
            "state": "active",
            "connected_at": 1234567890.0,
            "duration": 120.5,
            "idle_time": 5.2,
            "total_audio_received": 1024,
            "total_audio_sent": 2048,
            "segments_processed": 3,
            "transcript_entries": 2,
            "errors": 0,
        }

        mock_get_connections.return_value = [connection_state]

        response = test_client.get("/api/v1/call/stats")

        assert response.status_code == 200
        data = response.json()

        assert data["active_connections"] == 1
        assert "max_connections" in data
        assert isinstance(data["max_connections"], int)
        assert len(data["connections"]) == 1

        conn_stats = data["connections"][0]
        assert conn_stats["call_id"] == "test-call-123"
        assert conn_stats["user_id"] == "test-user-123"
        assert conn_stats["state"] == "active"
        assert conn_stats["segments_processed"] == 3


class TestErrorHandling:
    """Test suite for API error handling."""

    def test_404_endpoint(self, test_client: TestClient):
        """Test 404 error for non-existent endpoint."""
        response = test_client.get("/nonexistent")
        
        assert response.status_code == 404

    def test_method_not_allowed(self, test_client: TestClient):
        """Test 405 error for wrong HTTP method."""
        response = test_client.post("/")
        
        assert response.status_code == 405

    @patch("src.pipeline.state.connection_state_manager.get_connection_count")
    def test_health_endpoint_error(self, mock_get_count, test_client: TestClient):
        """Test health endpoint when connection manager fails."""
        mock_get_count.side_effect = Exception("Connection manager error")

        # This should cause an error since the health endpoint calls get_connection_count
        with pytest.raises(Exception):
            response = test_client.get("/health")


class TestCORSHeaders:
    """Test suite for CORS headers."""

    def test_cors_middleware_configured(self, test_client: TestClient):
        """Test that CORS middleware is configured in the app."""
        # The CORS middleware is configured in main.py
        # In test environment, headers might not be visible
        response = test_client.get("/")

        assert response.status_code == 200
        # Just verify the endpoint works - CORS headers are handled by middleware

    def test_options_request_handled(self, test_client: TestClient):
        """Test OPTIONS request handling."""
        # Test that the app can handle different HTTP methods
        response = test_client.get("/")
        assert response.status_code == 200

        # POST to root should return method not allowed
        response = test_client.post("/")
        assert response.status_code == 405


class TestAPIDocumentation:
    """Test suite for API documentation endpoints."""

    def test_openapi_json(self, test_client: TestClient):
        """Test OpenAPI JSON endpoint."""
        response = test_client.get("/api/v1/openapi.json")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "Cortexa Voice Gateway Service"

    def test_docs_endpoint(self, test_client: TestClient):
        """Test Swagger UI docs endpoint."""
        response = test_client.get("/api/v1/docs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    def test_redoc_endpoint(self, test_client: TestClient):
        """Test ReDoc documentation endpoint."""
        response = test_client.get("/api/v1/redoc")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]


class TestServiceConfiguration:
    """Test suite for service configuration in responses."""

    def test_service_name_consistency(self, test_client: TestClient, mock_settings):
        """Test that service name is consistent across endpoints."""
        # Root endpoint
        root_response = test_client.get("/")
        root_data = root_response.json()

        # Health endpoint
        health_response = test_client.get("/health")
        health_data = health_response.json()

        # Both should reference the same service
        assert "Cortexa Voice Gateway" in root_data["service"]
        # Health endpoint uses the settings service name
        assert health_data["service"] in ["voice-gateway", "voice-gateway-test"]

    def test_version_consistency(self, test_client: TestClient):
        """Test that version is consistent across endpoints."""
        root_response = test_client.get("/")
        root_data = root_response.json()
        
        openapi_response = test_client.get("/api/v1/openapi.json")
        openapi_data = openapi_response.json()
        
        assert root_data["version"] == openapi_data["info"]["version"]


class TestRateLimiting:
    """Test suite for rate limiting (if implemented)."""

    def test_multiple_requests_allowed(self, test_client: TestClient):
        """Test that multiple requests are allowed under normal conditions."""
        responses = []
        
        # Make multiple requests
        for _ in range(10):
            response = test_client.get("/health")
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200

    def test_concurrent_requests(self, test_client: TestClient):
        """Test handling of concurrent requests."""
        import concurrent.futures
        
        def make_request():
            return test_client.get("/health")
        
        # Make concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(5)]
            responses = [future.result() for future in futures]
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
