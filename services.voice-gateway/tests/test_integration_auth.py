"""
Integration tests for authentication and authorization flow.

These tests verify that the authentication and authorization system works
correctly when the voice-gateway service is running behind the APISIX gateway.
"""

import json
import base64
import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from src.core.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GatewayAuthParser
from src.core.authorization import AuthorizationEngine, Permission, Role, authorize_user_action


class TestGatewayAuthParser:
    """Test suite for gateway authentication parsing."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = GatewayAuthParser()
    
    def test_parse_valid_user_context(self):
        """Test parsing valid user context from headers."""
        # Create test user context
        user_context = {
            "user": {
                "id": "test-user-123",
                "email": "<EMAIL>",
                "metadata": {"plan": "premium"},
                "app_metadata": {"organization": "test-org"}
            },
            "token": {
                "kid": "test-key-1",
                "iss": "https://test.supabase.co/auth/v1",
                "aud": "authenticated",
                "exp": 9999999999,  # Far future
                "iat": 1234567890
            },
            "validated_at": "2024-01-15T10:30:00Z"
        }
        
        # Encode as base64
        user_context_b64 = base64.b64encode(json.dumps(user_context).encode()).decode()
        
        # Create headers
        headers = {
            "X-User-Context": user_context_b64,
            "X-User-ID": "test-user-123",
            "X-User-Email": "<EMAIL>",
        }
        
        # Parse user
        user = self.parser.parse_user_from_headers(headers)
        
        # Verify user information
        assert user is not None
        assert user.user_id == "test-user-123"
        assert user.email == "<EMAIL>"
        assert user.user_metadata["plan"] == "premium"
        assert user.token_issuer == "https://test.supabase.co/auth/v1"
    
    def test_parse_missing_user_context(self):
        """Test parsing when user context header is missing."""
        headers = {}
        user = self.parser.parse_user_from_headers(headers)
        assert user is None
    
    def test_parse_invalid_base64(self):
        """Test parsing with invalid base64 user context."""
        headers = {
            "X-User-Context": "invalid-base64!"
        }
        
        with pytest.raises(HTTPException) as exc_info:
            self.parser.parse_user_from_headers(headers)
        
        assert exc_info.value.status_code == 400
    
    def test_parse_invalid_json(self):
        """Test parsing with invalid JSON in user context."""
        invalid_json = base64.b64encode(b"invalid json").decode()
        headers = {
            "X-User-Context": invalid_json
        }
        
        with pytest.raises(HTTPException) as exc_info:
            self.parser.parse_user_from_headers(headers)
        
        assert exc_info.value.status_code == 400
    
    def test_parse_expired_token(self):
        """Test parsing with expired token."""
        user_context = {
            "user": {
                "id": "test-user-123",
                "email": "<EMAIL>",
                "metadata": {},
                "app_metadata": {}
            },
            "token": {
                "kid": "test-key-1",
                "iss": "https://test.supabase.co/auth/v1",
                "aud": "authenticated",
                "exp": 1234567890,  # Past timestamp
                "iat": 1234567800
            },
            "validated_at": "2024-01-15T10:30:00Z"
        }
        
        user_context_b64 = base64.b64encode(json.dumps(user_context).encode()).decode()
        headers = {"X-User-Context": user_context_b64}
        
        with pytest.raises(HTTPException) as exc_info:
            self.parser.parse_user_from_headers(headers)
        
        assert exc_info.value.status_code == 401


# Authorization engine tests removed since authorization was simplified


class TestIntegrationFlow:
    """Test suite for end-to-end integration flow."""
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_flow(self):
        """Test complete WebSocket authentication flow."""
        from src.api.v1.endpoints.call import websocket_endpoint
        from unittest.mock import MagicMock
        
        # Create mock WebSocket with gateway headers
        mock_websocket = MagicMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_json = AsyncMock()
        mock_websocket.close = AsyncMock()
        
        # Create user context
        user_context = {
            "user": {
                "id": "integration-test-user",
                "email": "<EMAIL>",
                "metadata": {},
                "app_metadata": {}
            },
            "token": {
                "kid": "test-key-1",
                "iss": "https://test.supabase.co/auth/v1",
                "aud": "authenticated",
                "exp": 9999999999,
                "iat": 1234567890
            },
            "validated_at": "2024-01-15T10:30:00Z"
        }
        
        user_context_b64 = base64.b64encode(json.dumps(user_context).encode()).decode()
        
        # Set up mock headers (simulating APISIX gateway)
        mock_websocket.headers = {
            "X-User-Context": user_context_b64,
            "X-User-ID": "integration-test-user",
            "X-User-Email": "<EMAIL>",
        }
        
        call_id = "integration-test-call"
        
        # Mock dependencies
        with patch("src.api.v1.endpoints.call.get_app_context") as mock_context, \
             patch("src.api.v1.endpoints.call.connection_state_manager") as mock_manager, \
             patch("src.api.v1.endpoints.call.run_pipeline") as mock_pipeline:
            
            # Set up mocks
            mock_app_context = Mock()
            mock_app_context.s2st_processor = Mock()
            mock_app_context.s2st_processor.is_initialized = True
            mock_context.return_value = mock_app_context
            
            mock_manager.add_connection = AsyncMock()
            mock_manager.remove_connection = AsyncMock()
            mock_pipeline.return_value = None
            
            # Execute WebSocket endpoint
            await websocket_endpoint(mock_websocket, call_id)
            
            # Verify authentication succeeded
            mock_websocket.accept.assert_called_once()
            mock_manager.add_connection.assert_called_once()
            
            # Verify connection state includes authenticated user
            connection_state = mock_manager.add_connection.call_args[0][0]
            assert connection_state.authenticated_user is not None
            assert connection_state.authenticated_user.user_id == "integration-test-user"
            assert connection_state.authenticated_user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_failure(self):
        """Test WebSocket authentication failure."""
        from src.api.v1.endpoints.call import websocket_endpoint
        from unittest.mock import MagicMock
        
        # Create mock WebSocket without authentication headers
        mock_websocket = MagicMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.close = AsyncMock()
        mock_websocket.headers = {}  # No authentication headers
        
        call_id = "test-call-no-auth"
        
        # Execute WebSocket endpoint
        await websocket_endpoint(mock_websocket, call_id)
        
        # Verify authentication failed
        mock_websocket.accept.assert_called_once()
        mock_websocket.close.assert_called_once()
        
        # Verify close was called with authentication error
        close_args = mock_websocket.close.call_args
        assert close_args[1]["code"] == 4001
        assert "Authentication required" in close_args[1]["reason"]
