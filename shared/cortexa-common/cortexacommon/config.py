from typing import Optional, Dict, Any, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    user: str = Field(default="cortexa_user", description="Database user")
    password: str = Field(default="cortexa_password", description="Database password")
    name: str = Field(default="cortexa", description="Database name")
    
    @property
    def url(self) -> str:
        """Get the database URL."""
        return f"postgresql+psycopg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class KafkaSettings(BaseSettings):
    """Kafka configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="KAFKA_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    bootstrap_servers: str = Field(default="localhost:9092", description="Kafka bootstrap servers")
    group_id: str = Field(default="cortexa-group", description="Kafka consumer group ID")


class SecuritySettings(BaseSettings):
    """Security configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="SECURITY_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    jwt_secret_key: Optional[str] = Field(
        default=None,
        description="JWT secret key for token signing"
    )
    jwt_algorithm: str = Field(
        default="HS256",
        description="JWT signing algorithm"
    )
    jwt_expiration_hours: int = Field(
        default=24,
        description="JWT token expiration in hours"
    )
    cors_origins: List[str] = Field(
        default=["*"],
        description="CORS allowed origins"
    )
    cors_allow_credentials: bool = Field(
        default=True,
        description="CORS allow credentials"
    )

    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v


class WebSocketSettings(BaseSettings):
    """WebSocket configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="WS_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    max_connections: int = Field(
        default=100,
        description="Maximum concurrent WebSocket connections"
    )
    heartbeat_interval: int = Field(
        default=30,
        description="WebSocket heartbeat interval in seconds"
    )
    connection_timeout: int = Field(
        default=300,
        description="WebSocket connection timeout in seconds"
    )
    message_queue_size: int = Field(
        default=100,
        description="Maximum message queue size per connection"
    )


class APISettings(BaseSettings):
    """API configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="API_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    title: str = Field(
        default="Cortexa API",
        description="API title"
    )
    description: str = Field(
        default="Cortexa microservice API",
        description="API description"
    )
    version: str = Field(
        default="1.0.0",
        description="API version"
    )
    docs_url: Optional[str] = Field(
        default="/docs",
        description="API documentation URL"
    )
    redoc_url: Optional[str] = Field(
        default="/redoc",
        description="ReDoc documentation URL"
    )
    openapi_url: Optional[str] = Field(
        default="/openapi.json",
        description="OpenAPI schema URL"
    )


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="LOG_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    level: str = Field(
        default="INFO",
        description="Log level"
    )
    format: str = Field(
        default="text",
        description="Log format (text, json)"
    )
    file: Optional[str] = Field(
        default=None,
        description="Log file path"
    )
    max_file_size: str = Field(
        default="100 MB",
        description="Maximum log file size"
    )
    retention: str = Field(
        default="30 days",
        description="Log file retention period"
    )


class MonitoringSettings(BaseSettings):
    """Monitoring configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="MONITORING_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    enabled: bool = Field(
        default=True,
        description="Enable monitoring"
    )
    metrics_enabled: bool = Field(
        default=True,
        description="Enable metrics collection"
    )
    tracing_enabled: bool = Field(
        default=True,
        description="Enable distributed tracing"
    )
    health_check_interval: int = Field(
        default=30,
        description="Health check interval in seconds"
    )


class BaseServiceSettings(BaseSettings):
    """Base settings for all microservices."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    service_name: str = Field(description="Name of the microservice")
    debug: bool = Field(default=False, description="Debug mode")
    host: str = Field(default="0.0.0.0", description="Service host")
    port: int = Field(default=8000, description="Service port")
    environment: str = Field(default="development", description="Environment (development, staging, production)")

    # Shared settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    kafka: KafkaSettings = Field(default_factory=KafkaSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    websocket: WebSocketSettings = Field(default_factory=WebSocketSettings)
    api: APISettings = Field(default_factory=APISettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"

    def get_service_url(self) -> str:
        """Get the service URL."""
        protocol = "https" if self.is_production else "http"
        return f"{protocol}://{self.host}:{self.port}"

    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return self.model_dump()

    def get_safe_dict(self) -> Dict[str, Any]:
        """Get settings dictionary with sensitive values masked."""
        data = self.to_dict()

        # Mask sensitive fields
        sensitive_keys = {
            "password", "secret", "key", "token", "jwt_secret",
            "anon_key", "service_role_key", "jwt_secret_key"
        }

        def mask_sensitive(obj, path=""):
            if isinstance(obj, dict):
                return {
                    k: mask_sensitive(v, f"{path}.{k}" if path else k)
                    for k, v in obj.items()
                }
            elif isinstance(obj, list):
                return [mask_sensitive(item, f"{path}[{i}]") for i, item in enumerate(obj)]
            elif isinstance(obj, str) and any(key in path.lower() for key in sensitive_keys):
                return "***masked***" if obj else None
            else:
                return obj

        return mask_sensitive(data)


# Global settings instance
_settings: Optional[BaseServiceSettings] = None


def get_settings() -> BaseServiceSettings:
    """Get or create the global settings instance."""
    global _settings
    if _settings is None:
        _settings = BaseServiceSettings(service_name="cortexa-common")
    return _settings
