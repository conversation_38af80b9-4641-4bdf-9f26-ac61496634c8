"""
Prometheus metrics abstraction for Cortexa services.

This module provides utilities for setting up Prometheus metrics collection
with FastAPI instrumentator and common metric types.
"""

from typing import Dict, Any, Optional, List, Callable
from fastapi import FastAPI

from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry
from prometheus_fastapi_instrumentator import Instrumentator, metrics

from .config import MetricsSettings
from ..logging import get_logger

logger = get_logger(__name__)


class MetricsManager:
    """Manages Prometheus metrics setup and collection."""
    
    def __init__(self, settings: MetricsSettings):
        """
        Initialize metrics manager.
        
        Args:
            settings: Metrics configuration settings
        """
        self.settings = settings
        self.instrumentator: Optional[Instrumentator] = None
        self.registry: Optional[CollectorRegistry] = None
        self._initialized = False
        self._custom_metrics: Dict[str, Any] = {}
    
    def setup_metrics(self, app: FastAPI) -> None:
        """
        Set up Prometheus metrics for FastAPI application.
        
        Args:
            app: FastAPI application instance
        """
        if not self.settings.enabled:
            logger.info("Metrics collection disabled")
            return
        
        try:
            # Initialize FastAPI instrumentator
            self.instrumentator = Instrumentator(
                should_group_status_codes=self.settings.should_group_status_codes,
                should_ignore_untemplated=self.settings.should_ignore_untemplated,
                should_respect_env_var=self.settings.should_respect_env_var,
                should_instrument_requests_inprogress=self.settings.should_instrument_requests_inprogress,
                excluded_handlers=self.settings.excluded_handlers,
                env_var_name="ENABLE_METRICS",
                inprogress_name="http_requests_inprogress",
                inprogress_labels=True,
            )
            
            # Add default metrics
            self.instrumentator.add(metrics.default())
            self.instrumentator.add(metrics.combined_size())
            
            # Add custom labels if specified
            if self.settings.custom_labels:
                self.instrumentator.add(
                    self._create_custom_labels_metric(self.settings.custom_labels)
                )
            
            # Instrument the app
            self.instrumentator.instrument(app)
            self.instrumentator.expose(
                app, 
                endpoint=self.settings.endpoint,
                include_in_schema=self.settings.include_in_schema
            )
            
            self._initialized = True
            logger.info(
                "Prometheus metrics initialized",
                endpoint=self.settings.endpoint,
                excluded_handlers=self.settings.excluded_handlers,
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize metrics: {e}")
    
    def _create_custom_labels_metric(self, labels: Dict[str, str]) -> Callable:
        """Create a metric function that adds custom labels."""
        def add_custom_labels(info):
            # This is a placeholder - in practice, you'd implement
            # custom label logic based on your requirements
            pass
        return add_custom_labels
    
    def create_counter(
        self,
        name: str,
        description: str,
        labels: Optional[List[str]] = None
    ) -> Counter:
        """
        Create a Prometheus Counter metric.

        Args:
            name: Metric name
            description: Metric description
            labels: Label names

        Returns:
            Counter instance
        """
        # Ensure labels is always a list, never None
        label_names = labels if labels is not None else []
        counter = Counter(name, description, label_names)
        self._custom_metrics[name] = counter
        logger.debug(f"Created counter metric: {name}")
        return counter
    
    def create_histogram(
        self,
        name: str,
        description: str,
        labels: Optional[List[str]] = None,
        buckets: Optional[List[float]] = None
    ) -> Histogram:
        """
        Create a Prometheus Histogram metric.

        Args:
            name: Metric name
            description: Metric description
            labels: Label names
            buckets: Histogram buckets

        Returns:
            Histogram instance
        """
        # Ensure labels is always a list, never None
        label_names = labels if labels is not None else []
        # Create histogram with proper parameters
        if buckets is not None:
            histogram = Histogram(name, description, label_names, buckets=buckets)
        else:
            histogram = Histogram(name, description, label_names)
        self._custom_metrics[name] = histogram
        logger.debug(f"Created histogram metric: {name}")
        return histogram
    
    def create_gauge(
        self,
        name: str,
        description: str,
        labels: Optional[List[str]] = None
    ) -> Gauge:
        """
        Create a Prometheus Gauge metric.

        Args:
            name: Metric name
            description: Metric description
            labels: Label names

        Returns:
            Gauge instance
        """
        # Ensure labels is always a list, never None
        label_names = labels if labels is not None else []
        gauge = Gauge(name, description, label_names)
        self._custom_metrics[name] = gauge
        logger.debug(f"Created gauge metric: {name}")
        return gauge
    
    def create_info(
        self,
        name: str,
        description: str
    ) -> Info:
        """
        Create a Prometheus Info metric.
        
        Args:
            name: Metric name
            description: Metric description
            
        Returns:
            Info instance
        """
        info = Info(name, description)
        self._custom_metrics[name] = info
        logger.debug(f"Created info metric: {name}")
        return info
    
    def get_metric(self, name: str) -> Optional[Any]:
        """
        Get a custom metric by name.
        
        Args:
            name: Metric name
            
        Returns:
            Metric instance if found
        """
        return self._custom_metrics.get(name)
    
    def is_initialized(self) -> bool:
        """Check if metrics are initialized."""
        return self._initialized


# Global metrics manager instance
_metrics_manager: Optional[MetricsManager] = None


def setup_metrics(app: FastAPI, settings: MetricsSettings) -> None:
    """
    Set up metrics with the given settings.
    
    Args:
        app: FastAPI application
        settings: Metrics configuration
    """
    global _metrics_manager
    
    _metrics_manager = MetricsManager(settings)
    _metrics_manager.setup_metrics(app)


def get_metrics_manager() -> Optional[MetricsManager]:
    """
    Get the global metrics manager.
    
    Returns:
        MetricsManager instance if available
    """
    return _metrics_manager


def create_counter(
    name: str,
    description: str,
    labels: Optional[List[str]] = None
) -> Optional[Counter]:
    """
    Create a counter metric using the global metrics manager.
    
    Args:
        name: Metric name
        description: Metric description
        labels: Label names
        
    Returns:
        Counter instance if metrics manager is available
    """
    if _metrics_manager:
        return _metrics_manager.create_counter(name, description, labels)
    return None


def create_histogram(
    name: str,
    description: str,
    labels: Optional[List[str]] = None,
    buckets: Optional[List[float]] = None
) -> Optional[Histogram]:
    """
    Create a histogram metric using the global metrics manager.
    
    Args:
        name: Metric name
        description: Metric description
        labels: Label names
        buckets: Histogram buckets
        
    Returns:
        Histogram instance if metrics manager is available
    """
    if _metrics_manager:
        return _metrics_manager.create_histogram(name, description, labels, buckets)
    return None


def create_gauge(
    name: str,
    description: str,
    labels: Optional[List[str]] = None
) -> Optional[Gauge]:
    """
    Create a gauge metric using the global metrics manager.
    
    Args:
        name: Metric name
        description: Metric description
        labels: Label names
        
    Returns:
        Gauge instance if metrics manager is available
    """
    if _metrics_manager:
        return _metrics_manager.create_gauge(name, description, labels)
    return None


def create_info(
    name: str,
    description: str
) -> Optional[Info]:
    """
    Create an info metric using the global metrics manager.
    
    Args:
        name: Metric name
        description: Metric description
        
    Returns:
        Info instance if metrics manager is available
    """
    if _metrics_manager:
        return _metrics_manager.create_info(name, description)
    return None


# Common metric types for microservices
class CommonMetrics:
    """Common metrics that most services will need."""
    
    def __init__(self, service_name: str):
        """
        Initialize common metrics for a service.

        Args:
            service_name: Name of the service
        """
        self.service_name = service_name

        # Convert service name to valid metric name (replace hyphens with underscores)
        metric_name = service_name.replace("-", "_")

        # Connection metrics
        self.active_connections = create_gauge(
            f"{metric_name}_active_connections",
            "Number of active connections"
        )

        self.total_connections = create_counter(
            f"{metric_name}_total_connections",
            "Total number of connections created"
        )

        # Request processing metrics
        self.processing_duration = create_histogram(
            f"{metric_name}_processing_duration_seconds",
            "Time spent processing requests",
            labels=["operation", "status"]
        )

        self.processing_errors = create_counter(
            f"{metric_name}_processing_errors_total",
            "Total number of processing errors",
            labels=["error_type", "component"]
        )

        # Service info
        self.service_info = create_info(
            f"{metric_name}_info",
            "Service information"
        )
    
    def record_connection_opened(self) -> None:
        """Record a new connection."""
        if self.total_connections:
            self.total_connections.inc()
    
    def record_active_connections(self, count: int) -> None:
        """Record the current number of active connections."""
        if self.active_connections:
            self.active_connections.set(count)
    
    def record_processing_time(
        self, 
        operation: str, 
        status: str, 
        duration: float
    ) -> None:
        """Record processing time for an operation."""
        if self.processing_duration:
            self.processing_duration.labels(
                operation=operation, 
                status=status
            ).observe(duration)
    
    def record_error(self, error_type: str, component: str) -> None:
        """Record a processing error."""
        if self.processing_errors:
            self.processing_errors.labels(
                error_type=error_type,
                component=component
            ).inc()
    
    def set_service_info(self, info: Dict[str, str]) -> None:
        """Set service information."""
        if self.service_info:
            self.service_info.info(info)
