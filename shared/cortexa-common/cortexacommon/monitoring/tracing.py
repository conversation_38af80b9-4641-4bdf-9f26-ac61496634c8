"""
OpenTelemetry tracing abstraction for Cortexa services.

This module provides a unified interface for setting up distributed tracing
with support for multiple exporters and automatic instrumentation.
"""

from typing import Optional, Dict, Any
from contextlib import contextmanager

from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter

from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.sdk.trace.sampling import TraceIdRatioBased

from .config import TracingSettings
from ..logging import get_logger

logger = get_logger(__name__)


class TracingManager:
    """Manages OpenTelemetry tracing setup and configuration."""
    
    def __init__(self, settings: TracingSettings):
        """
        Initialize tracing manager.
        
        Args:
            settings: Tracing configuration settings
        """
        self.settings = settings
        self.tracer_provider: Optional[TracerProvider] = None
        self.tracer: Optional[trace.Tracer] = None
        self._initialized = False
    
    def setup_tracing(self) -> Optional[trace.Tracer]:
        """
        Set up OpenTelemetry tracing.
        
        Returns:
            Tracer instance if tracing is enabled, None otherwise
        """
        if not self.settings.enabled:
            logger.info("Tracing disabled")
            return None
        
        try:
            # Create resource with service information
            resource_attributes = {
                "service.name": self.settings.service_name,
                "service.version": self.settings.service_version,
                "service.instance.id": self.settings.get_service_instance_id(),
            }
            
            # Add custom resource attributes
            resource_attributes.update(self.settings.resource_attributes)
            
            resource = Resource.create(resource_attributes)
            
            # Set up tracer provider with sampling
            sampler = TraceIdRatioBased(self.settings.sampling_ratio)
            self.tracer_provider = TracerProvider(
                resource=resource,
                sampler=sampler
            )
            trace.set_tracer_provider(self.tracer_provider)
            
            # Configure exporters
            self._setup_exporters()
            
            # Set up automatic instrumentation
            self._setup_instrumentation()
            
            # Get tracer for manual instrumentation
            self.tracer = trace.get_tracer(__name__)
            self._initialized = True
            
            logger.info(
                "OpenTelemetry tracing initialized",
                service=self.settings.service_name,
                version=self.settings.service_version,
                sampling_ratio=self.settings.sampling_ratio,
                otlp_enabled=bool(self.settings.otlp_endpoint),
            )
            
            return self.tracer
            
        except Exception as e:
            logger.error(f"Failed to initialize tracing: {e}")
            return None
    
    def _setup_exporters(self) -> None:
        """Set up trace exporters based on configuration."""
        if not self.tracer_provider:
            return
        
        exporters_configured = 0

        # OTLP exporter
        if self.settings.otlp_endpoint:
            try:
                otlp_exporter = OTLPSpanExporter(
                    endpoint=self.settings.otlp_endpoint,
                    headers=self.settings.otlp_headers or {}
                )
                
                self.tracer_provider.add_span_processor(
                    BatchSpanProcessor(otlp_exporter)
                )
                exporters_configured += 1
                logger.info(f"OTLP tracing configured: {self.settings.otlp_endpoint}")
                
            except Exception as e:
                logger.error(f"Failed to configure OTLP exporter: {e}")
        
        # Console exporter for development
        if exporters_configured == 0:
            logger.warning("No trace exporters configured, using console exporter")
            console_exporter = ConsoleSpanExporter()
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
    
    def _setup_instrumentation(self) -> None:
        """Set up automatic instrumentation for common libraries."""
        try:
            # FastAPI instrumentation
            FastAPIInstrumentor().instrument()
            logger.debug("FastAPI instrumentation enabled")

            # HTTP client instrumentation
            HTTPXClientInstrumentor().instrument()
            logger.debug("HTTPX instrumentation enabled")

            # Redis instrumentation
            RedisInstrumentor().instrument()
            logger.debug("Redis instrumentation enabled")

        except Exception as e:
            logger.error(f"Failed to set up automatic instrumentation: {e}")
    
    def get_tracer(self, name: Optional[str] = None) -> Optional[trace.Tracer]:
        """
        Get a tracer instance.
        
        Args:
            name: Tracer name (defaults to module name)
            
        Returns:
            Tracer instance if tracing is initialized
        """
        if not self._initialized:
            return None
        
        return trace.get_tracer(name or __name__)
    
    def shutdown(self) -> None:
        """Shutdown tracing and flush any pending spans."""
        if self.tracer_provider:
            try:
                self.tracer_provider.shutdown()
                logger.info("Tracing shutdown completed")
            except Exception as e:
                logger.error(f"Error during tracing shutdown: {e}")
            finally:
                self._initialized = False
                self.tracer_provider = None
                self.tracer = None


# Global tracing manager instance
_tracing_manager: Optional[TracingManager] = None


def setup_tracing(settings: TracingSettings) -> Optional[trace.Tracer]:
    """
    Set up tracing with the given settings.
    
    Args:
        settings: Tracing configuration
        
    Returns:
        Tracer instance if successful
    """
    global _tracing_manager
    
    _tracing_manager = TracingManager(settings)
    return _tracing_manager.setup_tracing()


def get_tracer(name: Optional[str] = None) -> Optional[trace.Tracer]:
    """
    Get a tracer instance.
    
    Args:
        name: Tracer name
        
    Returns:
        Tracer instance if available
    """
    if _tracing_manager:
        return _tracing_manager.get_tracer(name)
    return None


def shutdown_tracing() -> None:
    """Shutdown tracing."""
    global _tracing_manager
    
    if _tracing_manager:
        _tracing_manager.shutdown()
        _tracing_manager = None


@contextmanager
def trace_span(
    name: str,
    attributes: Optional[Dict[str, Any]] = None,
    tracer_name: Optional[str] = None
):
    """
    Context manager for creating trace spans.
    
    Args:
        name: Span name
        attributes: Span attributes
        tracer_name: Tracer name
        
    Yields:
        Span instance
    """
    tracer = get_tracer(tracer_name)
    if not tracer:
        # If tracing is not available, yield a no-op context
        yield None
        return
    
    with tracer.start_as_current_span(name) as span:
        if attributes:
            span.set_attributes(attributes)
        yield span


def add_span_attributes(attributes: Dict[str, Any]) -> None:
    """
    Add attributes to the current span.
    
    Args:
        attributes: Attributes to add
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        current_span.set_attributes(attributes)


def add_span_event(name: str, attributes: Optional[Dict[str, Any]] = None) -> None:
    """
    Add an event to the current span.
    
    Args:
        name: Event name
        attributes: Event attributes
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        current_span.add_event(name, attributes or {})


def set_span_status(status_code: trace.Status, description: Optional[str] = None) -> None:
    """
    Set the status of the current span.

    Args:
        status_code: Status code
        description: Status description
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        current_span.set_status(status_code, description)


class TracingMixin:
    """Mixin class to add tracing capabilities to other classes."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._tracer = get_tracer()

    def create_span(self, name: str, **attributes):
        """
        Create a new span with the given name and attributes.

        Args:
            name: Span name
            **attributes: Span attributes

        Returns:
            Context manager yielding a started span as current span
        """
        @contextmanager
        def _span_cm():
            tracer = self._tracer or trace.get_tracer(__name__)
            with tracer.start_as_current_span(name) as span:
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                yield span
        return _span_cm()

    def add_span_event(self, span: trace.Span, name: str, **attributes):
        """
        Add an event to a span.

        Args:
            span: Span to add event to
            name: Event name
            **attributes: Event attributes
        """
        span.add_event(name, attributes)

    def set_span_error(self, span: trace.Span, error: Exception):
        """
        Mark a span as having an error.

        Args:
            span: Span to mark as error
            error: Exception that occurred
        """
        span.set_status(Status(StatusCode.ERROR, str(error)))
        span.set_attribute("error", True)
        span.set_attribute("error.type", type(error).__name__)
        span.set_attribute("error.message", str(error))


def set_span_error(error: Exception, span: Optional[trace.Span] = None) -> None:
    """
    Mark a span as having an error (convenience function for current span).

    Args:
        error: Exception that occurred
        span: Span to mark as error (defaults to current span)
    """
    target_span = span or trace.get_current_span()
    if target_span and target_span.is_recording():
        target_span.set_status(Status(StatusCode.ERROR, str(error)))
        target_span.set_attribute("error", True)
        target_span.set_attribute("error.type", type(error).__name__)
        target_span.set_attribute("error.message", str(error))
