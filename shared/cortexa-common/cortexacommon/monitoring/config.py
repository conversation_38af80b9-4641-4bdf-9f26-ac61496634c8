"""
Monitoring configuration for Cortexa services.

This module provides configuration classes for tracing, metrics, and observability
settings that can be used across all microservices.
"""

import os
from typing import Optional, List, Dict
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class TracingSettings(BaseSettings):
    """OpenTelemetry tracing configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="TRACING_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    enabled: bool = Field(default=False, description="Enable distributed tracing")
    service_name: str = Field(description="Service name for tracing")
    service_version: str = Field(default="0.1.0", description="Service version")
    service_instance_id: Optional[str] = Field(
        default=None, 
        description="Service instance ID (defaults to hostname)"
    )

    # OTLP configuration (Grafana Tempo)
    otlp_endpoint: Optional[str] = Field(
        default=None,
        description="OTLP endpoint (e.g., http://localhost:4317)"
    )
    otlp_headers: Optional[Dict[str, str]] = Field(
        default=None,
        description="OTLP headers for authentication"
    )

    # Sampling configuration
    sampling_ratio: float = Field(
        default=1.0,
        description="Trace sampling ratio (0.0 to 1.0)"
    )

    # Resource attributes
    resource_attributes: Dict[str, str] = Field(
        default_factory=dict,
        description="Additional resource attributes"
    )

    def get_service_instance_id(self) -> str:
        """Get service instance ID, defaulting to hostname."""
        return self.service_instance_id or os.getenv("HOSTNAME", "unknown")


class MetricsSettings(BaseSettings):
    """Prometheus metrics configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="METRICS_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    enabled: bool = Field(default=True, description="Enable metrics collection")
    endpoint: str = Field(default="/metrics", description="Metrics endpoint path")
    include_in_schema: bool = Field(
        default=False, 
        description="Include metrics endpoint in OpenAPI schema"
    )
    
    # FastAPI instrumentator settings
    should_group_status_codes: bool = Field(
        default=False,
        description="Group HTTP status codes in metrics"
    )
    should_ignore_untemplated: bool = Field(
        default=True,
        description="Ignore untemplated routes"
    )
    should_respect_env_var: bool = Field(
        default=True,
        description="Respect ENABLE_METRICS environment variable"
    )
    should_instrument_requests_inprogress: bool = Field(
        default=True,
        description="Track requests in progress"
    )
    
    # Excluded handlers
    excluded_handlers: List[str] = Field(
        default_factory=lambda: ["/metrics", "/health", "/docs", "/redoc", "/openapi.json"],
        description="Handlers to exclude from metrics"
    )
    
    # Custom metric labels
    custom_labels: Dict[str, str] = Field(
        default_factory=dict,
        description="Custom labels to add to all metrics"
    )


class MonitoringSettings(BaseSettings):
    """Complete monitoring configuration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    service_name: str = Field(description="Service name")
    environment: str = Field(default="development", description="Environment name")
    
    # Component settings
    tracing: Optional[TracingSettings] = Field(default=None)
    metrics: MetricsSettings = Field(default_factory=MetricsSettings)

    def __init__(self, service_name: str, **kwargs):
        """Initialize monitoring settings with service name."""
        # Initialize tracing with service name if not provided
        if 'tracing' not in kwargs:
            kwargs['tracing'] = TracingSettings(service_name=service_name)

        super().__init__(service_name=service_name, **kwargs)


def get_monitoring_settings(service_name: str) -> MonitoringSettings:
    """
    Get monitoring settings for a service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        MonitoringSettings instance
    """
    return MonitoringSettings(service_name=service_name)
